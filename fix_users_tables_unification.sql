-- =====================================================
-- سكريبت توحيد جداول المستخدمين
-- حل مشكلة ازدواجية auth.users و public.users
-- =====================================================

-- 🔍 الخطوة 1: تحليل البيانات الحالية
DO $$
DECLARE
    auth_count INTEGER;
    public_count INTEGER;
    missing_in_auth INTEGER;
    missing_in_public INTEGER;
BEGIN
    -- عد المستخدمين في كل جدول
    SELECT COUNT(*) INTO auth_count FROM auth.users;
    SELECT COUNT(*) INTO public_count FROM public.users;
    
    -- البحث عن المستخدمين المفقودين
    SELECT COUNT(*) INTO missing_in_auth 
    FROM public.users p 
    WHERE NOT EXISTS (SELECT 1 FROM auth.users a WHERE a.id = p.id);
    
    SELECT COUNT(*) INTO missing_in_public 
    FROM auth.users a 
    WHERE NOT EXISTS (SELECT 1 FROM public.users p WHERE p.id = a.id);
    
    RAISE NOTICE '📊 تحليل البيانات الحالية:';
    RAISE NOTICE '   - المستخدمين في auth.users: %', auth_count;
    RAISE NOTICE '   - المستخدمين في public.users: %', public_count;
    RAISE NOTICE '   - مفقودين في auth.users: %', missing_in_auth;
    RAISE NOTICE '   - مفقودين في public.users: %', missing_in_public;
END $$;

-- 🔧 الخطوة 2: إنشاء جدول مستخدمين موحد جديد
DROP TABLE IF EXISTS public.users_unified CASCADE;

CREATE TABLE public.users_unified (
    -- المعرف الأساسي (من auth.users)
    id uuid PRIMARY KEY,
    
    -- بيانات المصادقة (من auth.users)
    email character varying(255) UNIQUE NOT NULL,
    email_confirmed_at timestamp with time zone,
    last_sign_in_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    
    -- البيانات الشخصية (من public.users)
    national_id character varying(20) UNIQUE NOT NULL,
    full_name character varying(255) NOT NULL,
    phone character varying(20),
    
    -- البيانات الوظيفية
    department character varying(255),
    position character varying(255),
    
    -- إعدادات الحساب
    is_active boolean DEFAULT true,
    is_admin boolean DEFAULT false,
    account_type character varying(20) DEFAULT 'user',
    max_devices integer DEFAULT 3,
    storage_quota_mb integer DEFAULT 1000,
    
    -- إدارة الجلسات
    last_seen timestamp with time zone,
    is_online boolean DEFAULT false,
    current_session_id uuid,
    total_sessions integer DEFAULT 0,
    
    -- بيانات الموقع
    last_location_lat double precision,
    last_location_lng double precision,
    last_location_name text,
    
    -- فهارس للبحث السريع
    CONSTRAINT users_unified_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT users_unified_national_id_check CHECK (length(national_id) >= 10)
);

-- إنشاء فهارس للأداء
CREATE INDEX idx_users_unified_email ON public.users_unified(email);
CREATE INDEX idx_users_unified_national_id ON public.users_unified(national_id);
CREATE INDEX idx_users_unified_is_active ON public.users_unified(is_active);
CREATE INDEX idx_users_unified_is_online ON public.users_unified(is_online);
CREATE INDEX idx_users_unified_last_seen ON public.users_unified(last_seen);
CREATE INDEX idx_users_unified_department ON public.users_unified(department);

-- 📋 الخطوة 3: دمج البيانات من الجدولين
INSERT INTO public.users_unified (
    id, email, email_confirmed_at, last_sign_in_at, created_at, updated_at,
    national_id, full_name, phone, department, position,
    is_active, is_admin, account_type, max_devices, storage_quota_mb,
    last_seen, is_online, current_session_id, total_sessions,
    last_location_lat, last_location_lng, last_location_name
)
SELECT 
    -- من auth.users
    a.id,
    a.email,
    a.email_confirmed_at,
    a.last_sign_in_at,
    a.created_at,
    a.updated_at,
    
    -- من public.users أو metadata
    COALESCE(p.national_id, (a.raw_user_meta_data->>'national_id')::varchar),
    COALESCE(p.full_name, (a.raw_user_meta_data->>'full_name')::varchar, 'مستخدم غير محدد'),
    p.phone,
    COALESCE(p.department, (a.raw_user_meta_data->>'department')::varchar),
    COALESCE(p.position, (a.raw_user_meta_data->>'position')::varchar),
    
    -- إعدادات الحساب
    COALESCE(p.is_active, true),
    COALESCE(p.is_admin, false),
    COALESCE(p.account_type, 'user'),
    COALESCE(p.max_devices, 3),
    COALESCE(p.storage_quota_mb, 1000),
    
    -- إدارة الجلسات
    p.last_seen,
    COALESCE(p.is_online, false),
    p.current_session_id,
    COALESCE(p.total_sessions, 0),
    
    -- بيانات الموقع
    p.last_location_lat,
    p.last_location_lng,
    p.last_location_name
    
FROM auth.users a
LEFT JOIN public.users p ON a.id = p.id
WHERE a.deleted_at IS NULL;

-- 🔄 الخطوة 4: إضافة المستخدمين المفقودين من public.users
INSERT INTO public.users_unified (
    id, email, national_id, full_name, phone, department, position,
    is_active, is_admin, account_type, max_devices, storage_quota_mb,
    last_seen, is_online, current_session_id, total_sessions,
    last_location_lat, last_location_lng, last_location_name,
    created_at, updated_at
)
SELECT
    p.id,
    p.email,
    p.national_id,
    p.full_name,
    p.phone,
    p.department,
    p.position,
    p.is_active,
    p.is_admin,
    p.account_type,
    p.max_devices,
    p.storage_quota_mb,
    p.last_seen,
    p.is_online,
    p.current_session_id,
    p.total_sessions,
    p.last_location_lat,
    p.last_location_lng,
    p.last_location_name,
    p.created_at,
    p.updated_at
FROM public.users p
WHERE NOT EXISTS (SELECT 1 FROM public.users_unified u WHERE u.id = p.id)
ON CONFLICT (id) DO NOTHING;

-- 📊 الخطوة 5: التحقق من نتائج الدمج
DO $$
DECLARE
    unified_count INTEGER;
    auth_count INTEGER;
    public_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO unified_count FROM public.users_unified;
    SELECT COUNT(*) INTO auth_count FROM auth.users WHERE deleted_at IS NULL;
    SELECT COUNT(*) INTO public_count FROM public.users;

    RAISE NOTICE '✅ نتائج الدمج:';
    RAISE NOTICE '   - المستخدمين في الجدول الموحد: %', unified_count;
    RAISE NOTICE '   - المستخدمين في auth.users: %', auth_count;
    RAISE NOTICE '   - المستخدمين في public.users: %', public_count;

    IF unified_count >= GREATEST(auth_count, public_count) THEN
        RAISE NOTICE '✅ تم الدمج بنجاح!';
    ELSE
        RAISE NOTICE '❌ هناك مشكلة في الدمج!';
    END IF;
END $$;

-- 🔄 الخطوة 6: إنشاء نسخة احتياطية من الجداول القديمة
CREATE TABLE IF NOT EXISTS public.users_backup AS SELECT * FROM public.users;
CREATE TABLE IF NOT EXISTS auth.users_backup AS SELECT * FROM auth.users;

-- 🔄 الخطوة 7: استبدال الجدول القديم بالجدول الموحد
DROP TABLE IF EXISTS public.users CASCADE;
ALTER TABLE public.users_unified RENAME TO users;

-- 🔧 الخطوة 8: إنشاء الدوال والمشاهد المحدثة
-- دالة لتحديث آخر ظهور
CREATE OR REPLACE FUNCTION update_user_last_seen(user_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.users
    SET
        last_seen = NOW(),
        is_online = true,
        updated_at = NOW()
    WHERE id = user_id;
END;
$$ LANGUAGE plpgsql;

-- دالة لبدء جلسة جديدة
CREATE OR REPLACE FUNCTION start_user_session_unified(
    p_user_id UUID,
    p_device_id UUID,
    p_app_version TEXT DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_location_lat DOUBLE PRECISION DEFAULT NULL,
    p_location_lng DOUBLE PRECISION DEFAULT NULL,
    p_location_name TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_session_id UUID;
BEGIN
    -- إنشاء جلسة جديدة
    INSERT INTO user_sessions (
        user_id, device_id, app_version, ip_address,
        location_lat, location_lng, location_name
    ) VALUES (
        p_user_id, p_device_id, p_app_version, p_ip_address,
        p_location_lat, p_location_lng, p_location_name
    ) RETURNING id INTO v_session_id;

    -- تحديث بيانات المستخدم
    UPDATE public.users
    SET
        is_online = true,
        current_session_id = v_session_id,
        last_seen = NOW(),
        total_sessions = total_sessions + 1,
        last_location_lat = COALESCE(p_location_lat, last_location_lat),
        last_location_lng = COALESCE(p_location_lng, last_location_lng),
        last_location_name = COALESCE(p_location_name, last_location_name),
        updated_at = NOW()
    WHERE id = p_user_id;

    -- تسجيل النشاط
    INSERT INTO user_activity_log (user_id, session_id, activity_type, location_lat, location_lng)
    VALUES (p_user_id, v_session_id, 'login', p_location_lat, p_location_lng);

    RETURN v_session_id;
END;
$$ LANGUAGE plpgsql;

-- إنشاء مشهد المستخدمين المتصلين المحدث
CREATE OR REPLACE VIEW v_online_users_unified AS
SELECT
    u.id, u.national_id, u.full_name, u.email, u.department, u.position,
    u.last_seen, u.is_online, u.is_active, u.current_session_id,
    u.last_location_name,
    s.id as session_id,
    d.device_name, d.device_model, d.device_brand, d.last_login as device_last_login,
    EXTRACT(EPOCH FROM (NOW() - u.last_seen))/60 as minutes_since_last_seen,
    CASE
        WHEN u.last_seen IS NULL THEN 'لم يسجل دخول'
        WHEN u.last_seen > NOW() - INTERVAL '2 minutes' THEN 'متصل الآن'
        WHEN u.last_seen > NOW() - INTERVAL '5 minutes' THEN 'نشط مؤخراً'
        WHEN u.last_seen > NOW() - INTERVAL '30 minutes' THEN 'غير نشط'
        ELSE 'غير متصل'
    END as status_text
FROM public.users u
LEFT JOIN user_sessions s ON u.current_session_id = s.id
LEFT JOIN devices d ON s.device_id = d.id AND s.device_id IS NOT NULL
WHERE u.is_active = true
ORDER BY u.last_seen DESC NULLS LAST;

-- 📋 الخطوة 9: تحديث الصلاحيات
GRANT ALL ON public.users TO postgres;
GRANT SELECT, INSERT, UPDATE ON public.users TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.users TO anon;

-- 🎯 الخطوة 10: التحقق النهائي
DO $$
DECLARE
    final_count INTEGER;
    online_count INTEGER;
    active_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO final_count FROM public.users;
    SELECT COUNT(*) INTO online_count FROM public.users WHERE is_online = true;
    SELECT COUNT(*) INTO active_count FROM public.users WHERE is_active = true;

    RAISE NOTICE '🎉 التحقق النهائي:';
    RAISE NOTICE '   - إجمالي المستخدمين: %', final_count;
    RAISE NOTICE '   - المستخدمين المتصلين: %', online_count;
    RAISE NOTICE '   - المستخدمين النشطين: %', active_count;
    RAISE NOTICE '✅ تم توحيد الجداول بنجاح!';
END $$;
